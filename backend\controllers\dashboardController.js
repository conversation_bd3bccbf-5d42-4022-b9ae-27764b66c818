const { db } = require('../config/database')
const Response = require('../utils/response')

// 获取仪表盘统计数据
const getStats = async (req, res) => {
  try {
    // 从appData集合获取数据
    const appData = await getAppData()
    
    const stats = {
      totalUsers: appData.totalUsers || 0,
      totalLogined: appData.totalLogined || 0,
      totalPost: appData.totalPost || 0,
      totalView: appData.totalView || 0
    }
    
    res.json(Response.success(stats))
  } catch (error) {
    console.error('获取统计数据失败:', error)
    res.status(500).json(Response.error('获取统计数据失败'))
  }
}

// 获取所有用户数据
const getAllUsers = async (req, res) => {
  const maxRetries = 3
  let retryCount = 0
  
  const performQuery = async () => {
    try {
      const { page = 1, limit = 20, search = '', searchType = 'nickName' } = req.query
      const skip = (page - 1) * limit

      console.log(`开始查询用户数据 (第${retryCount + 1}次尝试):`, { page, limit, search, searchType })

      let query = db.collection('users')

      // 如果有搜索关键词，添加搜索条件
      if (search && search.trim()) {
        const searchField = searchType === 'phoneNumber' ? 'phoneNumber' : 'nickName'
        const searchCondition = {}

        if (searchType === 'phoneNumber') {
          // 手机号精确匹配或部分匹配
          searchCondition[searchField] = db.RegExp({
            regexp: search.trim(),
            options: 'i'
          })
        } else {
          // 昵称模糊匹配
          searchCondition[searchField] = db.RegExp({
            regexp: search.trim(),
            options: 'i'
          })
        }

        query = query.where(searchCondition)
      }
      
      // 增加超时时间到15秒，并优化查询
      const result = await Promise.race([
        query
          .skip(skip)
          .limit(parseInt(limit))
          .orderBy('createTime', 'desc') // 添加排序提高查询效率
          .get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000) // 增加到15秒
        )
      ])
      
      console.log(`查询成功，获取到 ${result.data.length} 条用户数据`)
      
      // 使用简单的估算策略
      const estimatedTotal = Math.max(
        skip + result.data.length,
        result.data.length === parseInt(limit) ? (parseInt(page) + 1) * parseInt(limit) : skip + result.data.length
      )
      
      return res.json(Response.success({
        users: result.data,
        pagination: {
          total: estimatedTotal,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(estimatedTotal / limit),
          hasMore: result.data.length === parseInt(limit)
        }
      }))
    } catch (error) {
      console.error(`第${retryCount + 1}次查询失败:`, error.message)
      
      // 如果是超时错误且还有重试次数，进行重试
      if ((error.message.includes('timeout') || error.message.includes('查询超时')) && retryCount < maxRetries - 1) {
        retryCount++
        console.log(`将在2秒后进行第${retryCount + 1}次重试...`)
        await new Promise(resolve => setTimeout(resolve, 2000)) // 等待2秒后重试
        return performQuery()
      }
      
      // 重试次数用完或其他错误
      if (error.message.includes('timeout') || error.message.includes('查询超时')) {
        return res.status(500).json(Response.error('数据库连接超时，请检查网络连接或稍后重试'))
      } else {
        return res.status(500).json(Response.error('获取用户列表失败'))
      }
    }
  }
  
  return performQuery()
}

// 私有方法：从appData集合获取数据
const getAppData = async () => {
  try {
    console.log('开始从appData集合获取数据...')
    
    const result = await Promise.race([
      db.collection('appData').where({
        _id: '61493796683c0eae01a1f4045d9b0e0b'
      }).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data && result.data.length > 0) {
      const data = result.data[0]
      console.log('成功获取appData:', data)
      return {
        totalUsers: data.totalUsers || 0,
        totalLogined: data.totalLogined || 0,
        totalPost: data.totalPost || 0,
        totalView: data.totalView || 0
      }
    } else {
      console.log('未找到appData，返回默认值')
      return {
        totalUsers: 0,
        totalLogined: 0,
        totalPost: 0,
        totalView: 0
      }
    }
  } catch (error) {
    console.error('获取appData失败:', error.message)
    // 如果获取失败，返回默认值
    return {
      totalUsers: 0,
      totalLogined: 0,
      totalPost: 0,
      totalView: 0
    }
  }
}

// 获取单个用户详情
const getUserById = async (req, res) => {
  try {
    const { userId } = req.params
    console.log('获取用户详情:', userId)
    
    const result = await Promise.race([
      db.collection('users').doc(userId).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data.length > 0) {
      res.json(Response.success(result.data[0]))
    } else {
      res.status(404).json(Response.error('用户不存在'))
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    res.status(500).json(Response.error('获取用户详情失败'))
  }
}

// 更新用户信息
const updateUser = async (req, res) => {
  try {
    const { userId } = req.params
    const updateData = req.body
    
    console.log('更新用户信息:', userId, updateData)
    
    // 过滤不允许更新的字段
    const allowedFields = ['nickName', 'reward', 'province', 'remark']
    const filteredData = {}
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field]
      }
    })
    
    // 添加更新时间
    filteredData.updateTime = new Date().toISOString()
    
    const result = await Promise.race([
      db.collection('users').doc(userId).update(filteredData),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('更新超时')), 8000)
      )
    ])
    
    if (result.updated > 0) {
      // 获取更新后的用户信息
      const updatedUser = await db.collection('users').doc(userId).get()
      res.json(Response.success(updatedUser.data[0], '用户更新成功'))
    } else {
      res.status(404).json(Response.error('用户不存在或无更新'))
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    res.status(500).json(Response.error('更新用户失败'))
  }
}

module.exports = {
  getStats,
  getAllUsers,
  getUserById,
  updateUser
}